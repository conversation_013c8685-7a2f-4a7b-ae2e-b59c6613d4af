using Application.Abstraction;
using Domain.Identity;

namespace Infrastructure.Tests.Mocks;

/// <summary>
/// Mock implementace ICurrentUserService pro testování
/// </summary>
public class MockCurrentUserService : ICurrentUserService
{
    public string? UserId { get; set; } = "test-user";
    public string? Email { get; set; } = "<EMAIL>";
    public IEnumerable<string> Roles { get; set; } = new[] { "User" };
    public UserProfile? Profile { get; set; } = null;
}
