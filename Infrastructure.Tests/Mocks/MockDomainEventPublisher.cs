using Application.Abstraction.Mediator;
using Application.Services.Events;
using SharedKernel.Abstractions;
using SharedKernel.Models;

namespace Infrastructure.Tests.Mocks;

/// <summary>
/// Mock implementace DomainEventPublisher pro testování
/// </summary>
public class MockDomainEventPublisher : DomainEventPublisher
{
    public List<DomainEvent> PublishedEvents { get; } = new();

    public MockDomainEventPublisher() : base(new MockMediator())
    {
    }

    public new async Task Publish(DomainEvent domainEvent)
    {
        PublishedEvents.Add(domainEvent);
        await Task.CompletedTask;
    }
}

/// <summary>
/// Mock implementace IMediator pro testování
/// </summary>
public class MockMediator : IMediator
{
    public Task<TResponse> Send<TResponse>(IRequest<TResponse> request, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(default(TResponse)!);
    }

    public Task Send<TRequest>(TRequest request, CancellationToken cancellationToken = default) where TRequest : IRequest<Result<object>>
    {
        return Task.CompletedTask;
    }

    public Task Publish<TNotification>(TNotification notification, CancellationToken cancellationToken = default) where TNotification : INotification
    {
        return Task.CompletedTask;
    }
}
