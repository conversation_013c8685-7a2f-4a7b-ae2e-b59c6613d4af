using Infrastructure.Persistence.Interceptors;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.InMemory;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Application.Abstraction;
using SharedKernel.Abstractions;

namespace Infrastructure.Tests.Interceptors;

/// <summary>
/// Testy pro TrackableEntityInterceptor - ověření trackingu entit implementujících ITrackableEntity
/// </summary>
public class TrackableEntityInterceptorTests
{
    private class TestTrackableEntity : BaseTrackableEntity<int>
    {
        public string Name { get; set; } = string.Empty;
    }

    private class TestNonTrackableEntity : BaseEntity<int>
    {
        public string Name { get; set; } = string.Empty;
    }

    [Fact]
    public void Constructor_WithCurrentUserService_SetsService()
    {
        // Arrange
        var mockCurrentUserService = new Mock<ICurrentUserService>();

        // Act
        var interceptor = new TrackableEntityInterceptor(mockCurrentUserService.Object);

        // Assert
        Assert.NotNull(interceptor);
    }

    // Poznámka: Komplexnější testy interceptoru by vyžadovaly složitější setup
    // Pro nyní se spokojíme s testy konstruktoru a základní funkčnosti

    /// <summary>
    /// Testovací DbContext pro unit testy
    /// </summary>
    private class TestDbContext : DbContext
    {
        public TestDbContext(DbContextOptions<TestDbContext> options) : base(options) { }
        
        public DbSet<TestTrackableEntity> TrackableEntities { get; set; }
        public DbSet<TestNonTrackableEntity> NonTrackableEntities { get; set; }
    }
}
