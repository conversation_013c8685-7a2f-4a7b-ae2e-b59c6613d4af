using System.Reflection;
using Domain.System;
using Infrastructure.Persistence.Interceptors;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Moq;
using Xunit;
using Application.Abstraction;
using SharedKernel.Abstractions;
using SharedKernel.Attributes;

namespace Infrastructure.Tests.Interceptors;

/// <summary>
/// Testy pro AuditableEntityInterceptor - ověření auditování entit označených [Auditable]
/// </summary>
public class AuditableEntityInterceptorTests
{
    [Auditable("Test entity pro auditování")]
    private class TestAuditableEntity : BaseEntity<int>
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        
        [NotAudited("Citlivé informace")]
        public string SensitiveData { get; set; } = string.Empty;
    }

    private class TestNonAuditableEntity : BaseEntity<int>
    {
        public string Name { get; set; } = string.Empty;
    }

    [Fact]
    public void IsAuditable_EntityWithAuditableAttribute_ReturnsTrue()
    {
        // Arrange
        var entityType = typeof(TestAuditableEntity);

        // Act
        var isAuditable = IsAuditableMethod(entityType);

        // Assert
        Assert.True(isAuditable);
    }

    [Fact]
    public void IsAuditable_EntityWithoutAuditableAttribute_ReturnsFalse()
    {
        // Arrange
        var entityType = typeof(TestNonAuditableEntity);

        // Act
        var isAuditable = IsAuditableMethod(entityType);

        // Assert
        Assert.False(isAuditable);
    }

    [Fact]
    public void IsPropertyAuditable_PropertyWithoutNotAuditedAttribute_ReturnsTrue()
    {
        // Arrange
        var entityType = typeof(TestAuditableEntity);
        var propertyName = "Name";

        // Act
        var isAuditable = IsPropertyAuditableMethod(entityType, propertyName);

        // Assert
        Assert.True(isAuditable);
    }

    [Fact]
    public void IsPropertyAuditable_PropertyWithNotAuditedAttribute_ReturnsFalse()
    {
        // Arrange
        var entityType = typeof(TestAuditableEntity);
        var propertyName = "SensitiveData";

        // Act
        var isAuditable = IsPropertyAuditableMethod(entityType, propertyName);

        // Assert
        Assert.False(isAuditable);
    }

    [Fact]
    public void Constructor_WithCurrentUserService_SetsService()
    {
        // Arrange
        var mockCurrentUserService = new Mock<ICurrentUserService>();

        // Act
        var interceptor = new AuditableEntityInterceptor(mockCurrentUserService.Object);

        // Assert
        Assert.NotNull(interceptor);
    }

    /// <summary>
    /// Pomocná metoda pro testování privátní metody IsAuditable pomocí reflexe
    /// </summary>
    private static bool IsAuditableMethod(Type entityType)
    {
        var method = typeof(AuditableEntityInterceptor)
            .GetMethod("IsAuditable", BindingFlags.NonPublic | BindingFlags.Static);
        
        return (bool)method!.Invoke(null, new object[] { entityType })!;
    }

    /// <summary>
    /// Pomocná metoda pro testování privátní metody IsPropertyAuditable pomocí reflexe
    /// </summary>
    private static bool IsPropertyAuditableMethod(Type entityType, string propertyName)
    {
        var method = typeof(AuditableEntityInterceptor)
            .GetMethod("IsPropertyAuditable", BindingFlags.NonPublic | BindingFlags.Static);
        
        return (bool)method!.Invoke(null, new object[] { entityType, propertyName })!;
    }
}
