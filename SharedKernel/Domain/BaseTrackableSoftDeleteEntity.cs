namespace SharedKernel.Abstractions;

/// <summary>
/// Abstraktní třída kombinu<PERSON><PERSON><PERSON><PERSON> sledování změn a měkké mazání.
/// Poskytuje vlastnosti pro sledování vytvoření, modifikace i smazání entity.
/// </summary>
/// <typeparam name="T">Typ identifikátoru entity (např. int, Guid, string)</typeparam>
public abstract class BaseTrackableSoftDeleteEntity<T> : BaseTrackableEntity<T>, ISoftDelete
{
    /// <summary>
    /// Datum a čas smazání entity.
    /// Null hodnota znamená, že entita nebyla smazána.
    /// </summary>
    public DateTime? DeletedAt { get; set; }
    
    /// <summary>
    /// Identifikátor uživatele, který entitu smazal.
    /// </summary>
    public string? DeletedBy { get; set; }
}