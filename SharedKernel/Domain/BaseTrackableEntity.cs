namespace SharedKernel.Abstractions;

/// <summary>
/// Abstraktní třída pro entity, které vyžadují sledování změn.
/// Rozšiřuje základní entitu o vlastnosti pro sledování vytvoření a poslední modifikace.
/// </summary>
/// <typeparam name="T">Typ identifikátoru entity (např. int, Guid, string)</typeparam>
public abstract class BaseTrackableEntity<T> : BaseEntity<T>, ITrackableEntity<T>
{
    /// <summary>
    /// Datum a čas vytvoření entity.
    /// </summary>
    public DateTime? CreatedAt { get; set; }
    
    /// <summary>
    /// Identifikátor uživatele, který entitu vytvořil.
    /// </summary>
    public string? CreatedBy { get; set; }
    
    /// <summary>
    /// Datum a čas poslední modifikace entity.
    /// </summary>
    public DateTime? ModifiedAt { get; set; }
    
    /// <summary>
    /// Identifik<PERSON>tor uživatele, který entitu naposledy modifikoval.
    /// </summary>
    public string? ModifiedBy { get; set; }
}