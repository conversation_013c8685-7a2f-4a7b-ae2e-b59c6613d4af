namespace SharedKernel.Attributes;

/// <summary>
/// Atribut označující entitu jako auditovatelnou.
/// Entity označené tímto atributem budou mít své změny zaznamenávány do AuditTrail tabulky.
/// </summary>
[AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
public class AuditableAttribute : Attribute
{
    /// <summary>
    /// Inicializuje novou instanci AuditableAttribute.
    /// </summary>
    public AuditableAttribute()
    {
    }

    /// <summary>
    /// Inicializuje novou instanci AuditableAttribute s volitelným popisem.
    /// </summary>
    /// <param name="description">Popis účelu auditování této entity</param>
    public AuditableAttribute(string description)
    {
        Description = description;
    }

    /// <summary>
    /// Volitelný popis účelu auditování této entity.
    /// </summary>
    public string? Description { get; }
}
