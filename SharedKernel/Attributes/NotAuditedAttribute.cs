namespace SharedKernel.Attributes;

/// <summary>
/// Atribut označující vlastnost jako nevyžadující auditování.
/// Vlastnosti označené tímto atributem nebudou zahrnuty do AuditTrail záznamů,
/// i když je jejich třída označena jako [Auditable].
/// </summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false, Inherited = true)]
public class NotAuditedAttribute : Attribute
{
    /// <summary>
    /// Inicializuje novou instanci NotAuditedAttribute.
    /// </summary>
    public NotAuditedAttribute()
    {
    }

    /// <summary>
    /// Inicializuje novou instanci NotAuditedAttribute s volitelným důvodem.
    /// </summary>
    /// <param name="reason"><PERSON><PERSON><PERSON><PERSON>, proč tato vlastnost není auditována</param>
    public NotAuditedAttribute(string reason)
    {
        Reason = reason;
    }

    /// <summary>
    /// Volitelný důvod, proč tato vlastnost není auditována.
    /// </summary>
    public string? Reason { get; }
}
