using SharedKernel.Abstractions;
using SharedKernel.Attributes;

namespace Domain.Entities;

[Auditable("Ukázková entita pro demonstraci auditování")]
public class SampleEntity: BaseTrackableEntity<int>
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public int? Age { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public bool IsActive { get; set; }

    /// <summary>
    /// Interní poznámka - nebude auditována
    /// </summary>
    [NotAudited("Interní poznámka pro vývojáře")]
    public string? InternalNotes { get; set; }
}